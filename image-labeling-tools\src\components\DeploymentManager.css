/* DeploymentManager.css */
.deployment-manager {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.dm-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.dm-header h2 {
  color: white;
  margin: 0;
  font-size: 2rem;
}

/* 空状态 */
.dm-empty {
  text-align: center;
  padding: 4rem 2rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.dm-empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.dm-empty h3 {
  color: #333;
  margin-bottom: 1rem;
}

.dm-empty p {
  color: #666;
  margin-bottom: 2rem;
}

/* 部署网格 */
.dm-deployment-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 2rem;
}

.dm-deployment-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.dm-deployment-card:hover {
  transform: translateY(-5px);
}

.dm-deployment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #f0f0f0;
}

.dm-deployment-header h3 {
  margin: 0;
  color: #333;
  font-size: 1.4rem;
}

.dm-status-badge {
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  color: white;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

/* 部署信息 */
.dm-deployment-info {
  margin-bottom: 1.5rem;
}

.dm-info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.8rem;
  padding: 0.5rem 0;
}

.dm-label {
  font-weight: 600;
  color: #555;
  min-width: 80px;
}

.dm-url-link {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.dm-url-link:hover {
  color: #764ba2;
  text-decoration: underline;
}

/* 部署日志 */
.dm-deployment-logs {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 10px;
  border-left: 4px solid #667eea;
}

.dm-deployment-logs h4 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1rem;
}

.dm-logs {
  max-height: 200px;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
}

.dm-log-line {
  padding: 0.2rem 0;
  color: #333;
  border-bottom: 1px solid #e9ecef;
}

.dm-log-line:last-child {
  border-bottom: none;
}

/* 部署操作 */
.dm-deployment-actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

/* 模态框 */
.dm-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.dm-modal {
  background: white;
  border-radius: 20px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.dm-config-modal {
  max-width: 800px;
}

.dm-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem;
  border-bottom: 2px solid #f0f0f0;
}

.dm-modal-header h3 {
  margin: 0;
  color: #333;
  font-size: 1.5rem;
}

.dm-modal-header button {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #999;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.dm-modal-header button:hover {
  background: #f0f0f0;
  color: #333;
}

.dm-modal-body {
  padding: 2rem;
}

.dm-modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 2rem;
  border-top: 2px solid #f0f0f0;
}

/* 表单 */
.dm-form-group {
  margin-bottom: 1.5rem;
}

.dm-form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: #333;
  font-weight: 600;
}

.dm-form-group input,
.dm-form-group select {
  width: 100%;
  padding: 0.8rem;
  border: 2px solid #e0e0e0;
  border-radius: 10px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
  box-sizing: border-box;
}

.dm-form-group input:focus,
.dm-form-group select:focus {
  outline: none;
  border-color: #667eea;
}

.dm-form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

/* 配置部分 */
.dm-config-section {
  margin-bottom: 2rem;
}

.dm-config-section h4 {
  color: #333;
  margin-bottom: 1rem;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.dm-code-block {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 10px;
  padding: 1.5rem;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  line-height: 1.5;
  overflow-x: auto;
  color: #333;
  white-space: pre-wrap;
}

.dm-env-vars {
  background: #f8f9fa;
  border-radius: 10px;
  padding: 1rem;
}

.dm-env-var {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.8rem;
  margin-bottom: 0.5rem;
  background: white;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.dm-env-var:last-child {
  margin-bottom: 0;
}

.dm-env-var span:first-child {
  font-weight: 600;
  color: #333;
  font-family: 'Courier New', monospace;
}

.dm-env-var span:last-child {
  color: #666;
  font-family: 'Courier New', monospace;
  word-break: break-all;
}

/* 按钮 */
.btn {
  padding: 0.8rem 1.5rem;
  border: none;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.btn-primary {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background: #5a6268;
  transform: translateY(-2px);
}

.btn-danger {
  background: #dc3545;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background: #c82333;
  transform: translateY(-2px);
}

.btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .deployment-manager {
    padding: 1rem;
  }

  .dm-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .dm-deployment-grid {
    grid-template-columns: 1fr;
  }

  .dm-deployment-actions {
    justify-content: center;
  }

  .dm-form-row {
    grid-template-columns: 1fr;
  }

  .dm-modal {
    width: 95%;
    margin: 1rem;
  }

  .dm-info-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.3rem;
  }

  .dm-label {
    min-width: auto;
  }
}
