/* ProjectManager.css */
.project-manager {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* 顶部导航 */
.pm-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.pm-header-content h1 {
  margin: 0;
  font-size: 2.5rem;
  background: linear-gradient(45deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.pm-header-content p {
  margin: 0.5rem 0 0 0;
  color: #666;
  font-size: 1.1rem;
}

.pm-header-actions {
  display: flex;
  gap: 1rem;
}

/* 布局 */
.pm-layout {
  display: flex;
  min-height: calc(100vh - 120px);
}

.pm-sidebar {
  width: 250px;
  background: rgba(255, 255, 255, 0.9);
  padding: 2rem 0;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.pm-nav-item {
  padding: 1rem 2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border-left: 4px solid transparent;
}

.pm-nav-item:hover {
  background: rgba(102, 126, 234, 0.1);
  border-left-color: #667eea;
}

.pm-nav-item span {
  font-size: 1.1rem;
  color: #495057;
  transition: all 0.3s ease;
}

.pm-nav-item span.active {
  color: #667eea;
  font-weight: 600;
}

.pm-main {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
}

/* 仪表盘 */
.pm-dashboard {
  max-width: 1200px;
}

.pm-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.pm-stat-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 2rem;
  display: flex;
  align-items: center;
  gap: 1.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.pm-stat-card:hover {
  transform: translateY(-5px);
}

.pm-stat-icon {
  font-size: 3rem;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(45deg, #667eea, #764ba2);
  border-radius: 50%;
  color: white;
}

.pm-stat-info h3 {
  margin: 0;
  font-size: 2.5rem;
  color: #333;
  font-weight: 700;
}

.pm-stat-info p {
  margin: 0.5rem 0 0 0;
  color: #666;
  font-size: 1.1rem;
}

/* 项目网格 */
.pm-recent-projects h2 {
  color: white;
  margin-bottom: 2rem;
  font-size: 2rem;
}

.pm-project-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2rem;
}

.pm-project-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.pm-project-card:hover {
  transform: translateY(-5px);
}

.pm-project-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.pm-project-header h3 {
  margin: 0;
  color: #333;
  font-size: 1.3rem;
}

.pm-status-badge {
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  color: white;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.pm-priority-badge {
  padding: 0.2rem 0.6rem;
  border-radius: 10px;
  color: white;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
}

.pm-project-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 1rem 0;
  font-size: 0.9rem;
  color: #666;
}

.pm-project-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 1.5rem;
}

/* 项目列表 */
.pm-section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.pm-section-header h2 {
  color: white;
  margin: 0;
  font-size: 2rem;
}

.pm-projects-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.pm-project-item {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.pm-project-item:hover {
  transform: translateY(-2px);
}

.pm-project-info {
  flex: 1;
}

.pm-project-info h3 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 1.4rem;
}

.pm-project-info p {
  margin: 0 0 1rem 0;
  color: #666;
}

.pm-project-details {
  display: flex;
  gap: 2rem;
  font-size: 0.9rem;
  color: #888;
}

.pm-project-status {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  align-items: center;
  margin: 0 2rem;
}

.pm-project-actions {
  display: flex;
  gap: 0.5rem;
}

/* 甲方网格 */
.pm-clients-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2rem;
}

.pm-client-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.pm-client-card:hover {
  transform: translateY(-5px);
}

.pm-client-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #f0f0f0;
}

.pm-client-header h3 {
  margin: 0;
  color: #333;
  font-size: 1.4rem;
}

.pm-client-type {
  padding: 0.3rem 0.8rem;
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.pm-client-info p {
  margin: 0.5rem 0;
  color: #666;
  font-size: 0.95rem;
}

.pm-client-projects {
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 2px solid #f0f0f0;
}

/* 模态框 */
.pm-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.pm-modal {
  background: white;
  border-radius: 20px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.pm-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem;
  border-bottom: 2px solid #f0f0f0;
}

.pm-modal-header h3 {
  margin: 0;
  color: #333;
  font-size: 1.5rem;
}

.pm-modal-header button {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #999;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.pm-modal-header button:hover {
  background: #f0f0f0;
  color: #333;
}

.pm-modal-body {
  padding: 2rem;
}

.pm-modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 2rem;
  border-top: 2px solid #f0f0f0;
}

/* 表单 */
.pm-form-group {
  margin-bottom: 1.5rem;
}

.pm-form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: #333;
  font-weight: 600;
}

.pm-form-group input,
.pm-form-group select,
.pm-form-group textarea {
  width: 100%;
  padding: 0.8rem;
  border: 2px solid #e0e0e0;
  border-radius: 10px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
  box-sizing: border-box;
}

.pm-form-group input:focus,
.pm-form-group select:focus,
.pm-form-group textarea:focus {
  outline: none;
  border-color: #667eea;
}

.pm-form-group textarea {
  resize: vertical;
  min-height: 100px;
}

.pm-form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

/* 按钮 */
.btn {
  padding: 0.8rem 1.5rem;
  border: none;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.btn-primary {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #5a6268;
  transform: translateY(-2px);
}

.btn-danger {
  background: #dc3545;
  color: white;
}

.btn-danger:hover {
  background: #c82333;
  transform: translateY(-2px);
}

.btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .pm-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .pm-layout {
    flex-direction: column;
  }

  .pm-sidebar {
    width: 100%;
    display: flex;
    overflow-x: auto;
    padding: 1rem 0;
  }

  .pm-nav-item {
    white-space: nowrap;
    padding: 1rem;
    border-left: none;
    border-bottom: 4px solid transparent;
  }

  .pm-nav-item:hover {
    border-left: none;
    border-bottom-color: #667eea;
  }

  .pm-stats {
    grid-template-columns: 1fr;
  }

  .pm-project-grid {
    grid-template-columns: 1fr;
  }

  .pm-clients-grid {
    grid-template-columns: 1fr;
  }

  .pm-project-item {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .pm-project-status {
    margin: 0;
  }

  .pm-form-row {
    grid-template-columns: 1fr;
  }

  .pm-modal {
    width: 95%;
    margin: 1rem;
  }
}
